<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import download from "../../components/download-reports/download.vue";
import jsPDF from "jspdf";
import { capitalize } from "../../filters";
// import {Amiri} from "";

export default {
  name: "SaleAchievementComparisonReport",
  components: {
    download,
    vSelect,
  },
  data() {
    return {
      from_date: "",
      to_date: "",
      line_id: "",
      type_id: "",
      lines: [],
      types: [],
      dateLimit: "",
      items: [],
      fields: [],
      name: "Sales Achievement Comparison Report",
    }
  },
  computed: {
    stylingClasses() {
      return "col-lg-3 col-md-3 col-sm-8";
    },
    isShowingReport() {
      return this.line_id && this.type_id;
    }
  },
  methods: {
    setDates(date) {
      this.dateLimit = date
      if (!date) {
        this.from_date = moment().startOf("month").format("YYYY-MM-DD")
        this.to_date = moment().endOf("month").format("YYYY-MM-DD")
        return;
      }

      this.from_date = moment(date).startOf("month").format("YYYY-MM-DD")
      this.to_date = moment(date).endOf("month").format("YYYY-MM-DD")
    },
    async getLines() {
      const { res, error } = await this.tryCatch(axios.post('/api/sales-achievement-comparison/lines', {
        from: this.from_date,
        to: this.to_date,
      }));
      if (error) {
        this.showErrorMessage(error)
        return;
      }
      this.lines = res.data.data;
    },
    async getDivisionTypes() {
      const { res, error } = await this.tryCatch(axios.post('/api/sales-achievement-comparison/division-types', {
        from: this.from_date,
        to: this.to_date,
        line_id: this.line_id,
      }));
      if (error) {
        this.showErrorMessage(error)
        return;
      }
      this.types = res.data.data;
    },
    async getReportData() {
      const { res, error } = await this.tryCatch(axios.post('/api/sales-achievement-comparison', {
        from: this.from_date,
        to: this.to_date,
        line_id: this.line_id,
        division_type_id: this.type_id,
      }));
      if (error) {
        this.showErrorMessage(error)
        return;
      }
      this.items = res.data.data;
      this.fields = Object.keys(res.data.data[0]);
    },
    print() {
      this.$htmlToPaper("print");
    },
    download() {
      this.downloadXlsx(this.items, `${this.name}.xlsx`);
      this.$emit("downloaded");
    },
    downloadCsv() {
      this.downloadXlsx(this.items, `${this.name}.csv`);
      this.$emit("downloaded");
    },
    async createPDF() {
      let pdfName = this.name.replaceAll(' ', '');
      const columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      const Amiri = await import("../../assets/fonts/Amiri-Regular-normal")
      doc.addFileToVFS("Amiri-Regular.ttf", Amiri.Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
  },
  async created() {
    const { res, error } = await this.tryCatch(axios.get('/api/sales/date/limit'))

    if (error) {
      this.showErrorMessage(error)
      return;
    }

    this.setDates(res.data.date)

  },
}
</script>
<template>
  <div>
    <c-card>
      <c-card-header>Sales Details Report</c-card-header>
      <c-card-body>
        <div class="row">
          <div :class="stylingClasses">
            <c-input label="From Date" type="date" placeholder="Date" v-model="from_date" :max="dateLimit"></c-input>
          </div>
          <div :class="stylingClasses">
            <c-input label="To Date" type="date" placeholder="Date" v-model="to_date" :max="dateLimit"
              @input="getLines"></c-input>
          </div>
          <div :class="stylingClasses">
            <div class="form-group">
              <label> Line </label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Line" @input="getDivisionTypes" />
            </div>
          </div>
          <div :class="stylingClasses">
            <div class="form-group">
              <label>Types</label>
              <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                placeholder="Select Type" />
            </div>
          </div>

        </div>
      </c-card-body>
      <c-card-footer>
        <c-button v-if="isShowingReport" @click="getReportData" color="primary" class="text-white" style="float: right">
          Show
        </c-button>
      </c-card-footer>
    </c-card>
    <c-card v-if="items.length !== 0">
      <c-card-header>
        <h3 class="text-center">
          {{ name }} <br />
          from: {{ from_date }} to: {{ to_date }}
        </h3>
      </c-card-header>
      <c-card-body>
        <c-data-table ref="content" hover header tableFilter striped sorter footer :items="items" :fields="fields"
          :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top id="print">
          <template v-for="field in fields" #[field]="{ item }">
            <td :key="field" >
              <div v-if="item[field] !== undefined">{{ item[field] }}</div>
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :fields="fields"
          :data="items" :name="name" />
      </c-card-footer>
    </c-card>
  </div>

</template>

<style scoped></style>
